/**
 * 滴答清单API云对象配置文件
 * 统一管理所有API URLs、配置信息和常量
 */

// ================================
// 微信开放平台相关配置
// ================================

// 微信登录相关URL
const WECHAT_URLS = {
	// 获取微信登录二维码的基础URL
	qr_base_url: "https://open.weixin.qq.com/connect/qrconnect",

	// 微信二维码图片的基础URL
	qr_image_base_url: "https://open.weixin.qq.com/connect/qrcode",

	// 微信长轮询检查登录状态的URL
	poll_login_url: "https://long.open.weixin.qq.com/connect/l/qrconnect",

	// 微信登录重定向回调地址（滴答清单配置的）
	redirect_uri: "https://dida365.com/sign/wechat",
};

// 微信应用配置
const WECHAT_CONFIG = {
	// 滴答清单在微信开放平台的应用ID
	app_id: "wxf1429a73d311aad4",

	// OAuth2.0授权类型
	response_type: "code",

	// 授权作用域（网站应用微信登录）
	scope: "snsapi_login",

	// 默认状态参数（用于防CSRF攻击）
	default_state: "Lw==",
};

// ================================
// 滴答清单API相关配置
// ================================

// 滴答清单API基础配置
const DIDA_API_BASE = {
	// 滴答清单API v2版本基础URL
	base_url: "https://api.dida365.com/api/v2",

	// 滴答清单Web端主域名
	web_domain: "https://dida365.com",
};

// 滴答清单认证相关API
const DIDA_AUTH_APIS = {
	// 微信登录验证接口
	wechat_validate: "/user/sign/wechat/validate",

	// 密码登录接口
	password_login: "/user/signon",

	// 用户信息接口
	user_profile: "/user/profile",
};

// 滴答清单任务管理API
const DIDA_TASK_APIS = {
	// 批量检查/获取所有任务接口
	get_all_tasks: "/batch/check/0",

	// 获取已完成任务接口（支持分页）
	get_completed_tasks: "/project/all/closed",

	// 获取垃圾桶任务接口
	get_trash_tasks: "/project/all/trash/page",

	// 任务搜索接口
	task_search: "/task/search",

	// 任务统计接口（需要拼接日期范围）
	task_statistics: "/task/statistics", // /task/statistics/{start_date}/{end_date}
};

// 滴答清单项目管理API
const DIDA_PROJECT_APIS = {
	// 获取所有项目列表
	get_projects: "/projects",
};

// 滴答清单统计相关API
const DIDA_STATISTICS_APIS = {
	// 用户排名统计
	user_ranking: "/user/ranking",

	// 通用统计信息（概览、成就值、趋势）
	general_statistics: "/statistics/general",

	// 任务统计（需要拼接日期范围）
	task_statistics: "/task/statistics", // /task/statistics/{start_date}/{end_date}
};

// 滴答清单番茄专注API
const DIDA_POMODORO_APIS = {
	// 番茄专注概览（桌面版）
	general_for_desktop: "/pomodoros/statistics/generalForDesktop",

	// 专注详情分布（需要拼接日期范围）
	focus_distribution: "/pomodoros/statistics/dist", // /pomodoros/statistics/dist/{start_date}/{end_date}

	// 专注记录时间线
	focus_timeline: "/pomodoros/timeline",

	// 专注趋势热力图（需要拼接日期范围）
	focus_heatmap: "/pomodoros/statistics/heatmap", // /pomodoros/statistics/heatmap/{start_date}/{end_date}

	// 专注时间分布（按时间段，需要拼接日期范围）
	focus_time_distribution: "/pomodoros/statistics/dist/clockByDay", // /pomodoros/statistics/dist/clockByDay/{start_date}/{end_date}

	// 专注时间按小时分布（需要拼接日期范围）
	focus_hour_distribution: "/pomodoros/statistics/dist/clock", // /pomodoros/statistics/dist/clock/{start_date}/{end_date}
};

// 滴答清单习惯管理API
const DIDA_HABIT_APIS = {
	// 获取所有习惯
	get_habits: "/habits",

	// 本周习惯打卡统计
	week_current_statistics: "/habits/statistics/week/current",

	// 导出习惯数据（Excel格式）
	export_habits: "/data/export/habits",
};

// 滴答清单用户相关API
const DIDA_USER_APIS = {
	// 用户信息
	user_profile: "/user/profile",
};

// ================================
// 请求配置
// ================================

const REQUEST_CONFIG = {
	// 默认超时时间（毫秒）
	timeout: 30000,

	// 默认User-Agent
	user_agent:
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",

	// 默认设备信息
	device_info: "{}",

	// 默认语言
	language: "zh_CN",

	// 默认时区
	timezone: "Asia/Shanghai",
};

// ================================
// 错误代码定义
// ================================

const ERROR_CODES = {
	// 系统错误
	PARAM_IS_NULL: "PARAM_IS_NULL",
	PARAM_TYPE_ERROR: "PARAM_TYPE_ERROR",
	PARAM_FORMAT_ERROR: "PARAM_FORMAT_ERROR",
	UNAUTHORIZED: "UNAUTHORIZED",
	NETWORK_ERROR: "NETWORK_ERROR",
	TIMEOUT_ERROR: "TIMEOUT_ERROR",
	UNKNOWN_ERROR: "UNKNOWN_ERROR",

	// 业务错误
	AUTH_SESSION_EXPIRED: "AUTH_SESSION_EXPIRED",
	TASK_NOT_FOUND: "TASK_NOT_FOUND",
	EXPORT_FAILED: "EXPORT_FAILED",
	LOGIN_FAILED: "LOGIN_FAILED",
};

// ================================
// URL构建辅助函数
// ================================

/**
 * 构建微信登录二维码URL
 * @param {string} state - 状态参数，默认使用配置中的值
 * @returns {string} 完整的微信登录二维码URL
 */
function buildWeChatQRUrl(state = null) {
	if (state === null) {
		state = WECHAT_CONFIG.default_state;
	}

	const params = {
		appid: WECHAT_CONFIG.app_id,
		redirect_uri: WECHAT_URLS.redirect_uri,
		response_type: WECHAT_CONFIG.response_type,
		scope: WECHAT_CONFIG.scope,
		state: state,
	};

	// 构建查询字符串
	const queryString = Object.entries(params)
		.map(([k, v]) => `${k}=${encodeURIComponent(v)}`)
		.join("&");
	return `${WECHAT_URLS.qr_base_url}?${queryString}`;
}

/**
 * 构建微信登录状态轮询URL
 * @param {string} uuid - 二维码密钥
 * @param {number} timestamp - 时间戳，用于防缓存
 * @returns {string} 完整的轮询URL
 */
function buildWeChatPollUrl(uuid, timestamp = null) {
	if (timestamp === null) {
		timestamp = Date.now();
	}

	return `${WECHAT_URLS.poll_login_url}?uuid=${uuid}&_=${timestamp}`;
}

/**
 * 构建滴答清单API完整URL
 * @param {string} endpoint - API端点路径
 * @returns {string} 完整的API URL
 */
function buildDidaApiUrl(endpoint) {
	// 确保endpoint以/开头
	if (!endpoint.startsWith("/")) {
		endpoint = "/" + endpoint;
	}

	return `${DIDA_API_BASE.base_url}${endpoint}`;
}

/**
 * 构建微信登录验证URL
 * @param {string} code - 微信授权码
 * @param {string} state - 状态参数
 * @returns {string} 完整的验证URL
 */
function buildWeChatValidateUrl(code, state = null) {
	if (state === null) {
		state = WECHAT_CONFIG.default_state;
	}

	const baseUrl = buildDidaApiUrl(DIDA_AUTH_APIS.wechat_validate);
	return `${baseUrl}?code=${code}&state=${state}`;
}

/**
 * 构建密码登录URL
 * @param {boolean} wc - Web客户端标识，默认为true
 * @param {boolean} remember - 记住登录状态，默认为true
 * @returns {string} 完整的密码登录URL
 */
function buildPasswordLoginUrl(wc = true, remember = true) {
	const baseUrl = buildDidaApiUrl(DIDA_AUTH_APIS.password_login);
	const params = [];
	if (wc) {
		params.push("wc=true");
	}
	if (remember) {
		params.push("remember=true");
	}

	if (params.length > 0) {
		return `${baseUrl}?${params.join("&")}`;
	}
	return baseUrl;
}

// ================================
// 导出配置
// ================================

module.exports = {
	// URL配置
	WECHAT_URLS,
	WECHAT_CONFIG,
	DIDA_API_BASE,

	// API端点配置
	DIDA_AUTH_APIS,
	DIDA_TASK_APIS,
	DIDA_PROJECT_APIS,
	DIDA_STATISTICS_APIS,
	DIDA_POMODORO_APIS,
	DIDA_HABIT_APIS,
	DIDA_USER_APIS,

	// 其他配置
	REQUEST_CONFIG,
	ERROR_CODES,

	// 工具函数
	buildWeChatQRUrl,
	buildWeChatPollUrl,
	buildDidaApiUrl,
	buildWeChatValidateUrl,
	buildPasswordLoginUrl,
};
