<template>
  <view class="training-container">
    <z-page-navbar title="普通话训练">
      <template #right>
        <view class="navbar-actions">
          <view class="action-btn" @click="handleShowRecordList" aria-label="训练记录">
            <i class="fas fa-archive"></i>
          </view>
          <view class="navbar-pinyin-toggle" @click="showPinyin = !showPinyin">
            <text>{{ showPinyin ? '隐藏' : '显示' }}拼音</text>
          </view>
        </view>
      </template>
    </z-page-navbar>

    <!-- 单字库 -->
    <view class="word-bank-section" :class="{ collapsed: isWordBankCollapsed }">
      <view class="word-bank-header">
        <view class="word-bank-title-wrapper">
          <view class="word-bank-title">我的字库</view>
          <view v-if="isWordBankCollapsed && currentWord" class="current-word-display">
            <text>当前：{{ currentWord }}</text>
          </view>
        </view>
        <view class="header-actions">
          <view v-if="currentWord" class="regenerate-btn" @click="handleRegenerateContent" title="重新生成">
            <i class="fas fa-sync-alt"></i>
          </view>
          <view class="collapse-icon" @click="isWordBankCollapsed = !isWordBankCollapsed">
            <i class="fas" :class="isWordBankCollapsed ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
          </view>
        </view>
      </view>
      <view v-show="!isWordBankCollapsed" class="word-bank-content">
        <view v-for="(words, category) in wordBank" :key="category" class="category-group">
          <view class="category-name-tag">{{ category }}</view>
          <view v-for="word in words" :key="word" class="history-tag" :class="{ active: currentWord === word }"
            @click="handleTagClick(word)">
            {{ word }}
          </view>
        </view>
        <view class="add-tag" @click="openAddModal">
          <i class="fas fa-plus"></i>
        </view>
      </view>
    </view>

    <!-- 内容展示 -->
    <view class="content-section">
      <!-- 数据加载状态提示 -->
      <view v-if="isLoadingPutonghuaData" class="data-loading-hint">
        <z-loading text="正在加载训练数据..." :font-size="32" padding="40rpx 0" />
      </view>

      <!-- 数据错误提示 -->
      <view v-else-if="putonghuaDataError && !hasCloudData" class="data-error-hint">
        <view class="error-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </view>
        <text class="error-text">{{ putonghuaDataError }}</text>
        <view class="error-actions">
          <view class="retry-btn" @click="retryLoadData">
            <i class="fas fa-redo"></i>
            <text>重试</text>
          </view>
        </view>
      </view>

      <!-- 数据源状态提示 -->
      <view v-else-if="putonghuaDataError && hasCloudData" class="data-warning-hint">
        <view class="warning-icon">
          <i class="fas fa-wifi"></i>
        </view>
        <text class="warning-text">{{ putonghuaDataError }}</text>
      </view>

      <!-- 调试信息 -->
      <view v-if="false" style="background: #f0f0f0; padding: 10px; margin: 10px; font-size: 12px">
        <text>DEBUG INFO:</text><br />
        <text>currentWord: {{ currentWord }}</text><br />
        <text>contentLoading: {{ contentLoading }}</text><br />
        <text>hasCloudData: {{ hasCloudData }}</text><br />
        <text>allPutonghuaData.length: {{ allPutonghuaData.length }}</text><br />
        <text>generatedContent: {{ JSON.stringify(generatedContent) }}</text><br />
        <text>generatedContent.wordsAndPhrases: {{ generatedContent?.wordsAndPhrases?.length || 'undefined'
        }}</text><br />
        <text>generatedContent.sentences: {{ generatedContent?.sentences?.length || 'undefined' }}</text><br />
        <text>generatedContent.paragraph: {{ generatedContent?.paragraph ? 'exists' : 'undefined' }}</text><br />
      </view>

      <z-loading v-if="contentLoading" class="loading-spinner"></z-loading>
      <template v-else>
        <!-- 词语和成语 -->
        <view class="content-block"
          v-if="generatedContent && generatedContent.wordsAndPhrases && generatedContent.wordsAndPhrases.length > 0">
          <view class="content-title">
            <i class="fas fa-book-open"></i>
            <text>词语和成语</text>
          </view>
          <view class="phrase-container">
            <view class="phrase-item" v-for="(item, index) in generatedContent?.wordsAndPhrases || []" :key="index">
              <view class="text-pinyin-container">
                <view class="character-row">
                  <view class="character-item" v-for="(char, charIndex) in splitTextIntoCharacters(item.text)"
                    :key="charIndex">
                    <view class="pinyin-text" v-if="showPinyin">
                      {{ getPinyinForCharacter(item.pinyin, charIndex) }}
                    </view>
                    <view class="chinese-text">{{ char }}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 短句 -->
        <view class="content-block"
          v-if="generatedContent && generatedContent.sentences && generatedContent.sentences.length > 0">
          <view class="content-title">
            <i class="fas fa-quote-left"></i>
            <text>对应句子</text>
          </view>
          <view class="sentence-item" v-for="(item, index) in generatedContent?.sentences || []" :key="index">
            <view class="text-pinyin-container">
              <view class="character-row">
                <view class="character-item" v-for="(char, charIndex) in splitTextIntoCharacters(item.text)"
                  :key="charIndex">
                  <view class="pinyin-text" v-if="showPinyin">
                    {{ getPinyinForCharacter(item.pinyin, charIndex) }}
                  </view>
                  <view class="chinese-text">{{ char }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 综合段落 -->
        <view class="content-block" v-if="generatedContent && generatedContent.paragraph">
          <view class="content-title">
            <i class="fas fa-paragraph"></i>
            <text>综合段落</text>
          </view>
          <view class="paragraph-item">
            <view class="text-pinyin-container">
              <view class="character-row">
                <view class="character-item"
                  v-for="(char, charIndex) in splitTextIntoCharacters(generatedContent.paragraph.text)"
                  :key="charIndex">
                  <view class="pinyin-text" v-if="showPinyin">
                    {{ getPinyinForCharacter(generatedContent.paragraph.pinyin, charIndex) }}
                  </view>
                  <view class="chinese-text">{{ char }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 录音练习区域 -->
        <view class="content-block recording-practice-block" v-if="currentWord">
          <view class="content-title">
            <i class="fas fa-microphone"></i>
            <text>录音练习</text>
          </view>

          <!-- 录音播放器 -->
          <view v-if="currentAudioURL" class="audio-player-section">
            <z-audio-player :src="currentAudioURL" :enableTranscription="true" :initial-sentences="currentSentences"
              :showTime="true" :themeColor="'var(--color-primary, #007aff)'" class="practice-audio-player"
              ref="audioPlayerRef" @play="onAudioPlay" @pause="onAudioPause" @ended="onAudioEnded"
              @transcription-start="handleTranscriptionStart" @transcription-end="handleTranscriptionEnd" />
          </view>

          <!-- 录音提示文本 -->
          <view v-else class="recording-hint">
            <text>请录制"{{ currentWord }}"的发音练习</text>
          </view>
        </view>
      </template>
    </view>

    <!-- 录音输入区域 -->
    <view class="recording-input-container" v-if="currentWord">
      <z-message-input v-model="inputMessage" placeholder="点击麦克风开始录音练习..." @send-audio="handleAudioSubmit"
        @upload-progress="handleUploadProgress" @error="handleRecordError" :max-duration="60000" audio-format="mp3"
        cloud-path="speak/" mode="audio-only" />
    </view>

    <!-- 添加单字弹窗 -->
    <u-modal v-model="showAddModal" title="添加单字" showCancelButton @confirm="addHistoryTag" @cancel="closeAddModal"
      :async-close="true">
      <view class="modal-content">
        <u-input v-model="newTagInput" placeholder="请输入单个汉字" maxlength="1" fontSize="16px" clearable></u-input>
        <view class="category-selector">
          <view class="category-title">分类</view>
          <view class="category-tags">
            <view v-for="category in categories" :key="category" class="category-tag"
              :class="{ active: selectedCategory === category }" @click="selectedCategory = category">
              {{ category }}
            </view>
          </view>
        </view>
      </view>
    </u-modal>
  </view>

  <!-- 训练记录弹窗 -->
  <uni-popup ref="recordListPopupRef" type="bottom" @change="handleRecordListPopupChange" style="z-index: 10000">
    <view class="record-popup-content">
      <view class="record-popup-header">
        <text class="record-popup-title">{{ currentWord ? `"${currentWord}"的训练记录` : '训练记录' }}</text>
        <view class="header-actions">
          <text v-if="currentWord && filteredRecords.length > 0" class="clear-all-btn"
            @click="handleClearCurrentRecords">清空记录</text>
          <view class="record-popup-close-icon" @click="closeRecordListPopup">
            <uni-icons type="closeempty" size="20"></uni-icons>
          </view>
        </view>
      </view>
      <scroll-view scroll-y class="record-popup-scroll-view">
        <z-loading v-if="isLoadingRecords" text="正在加载..." :font-size="32" padding="40rpx 0" />
        <view v-else-if="!filteredRecords.length" class="record-popup-empty-state">
          <text>{{ currentWord ? `暂无"${currentWord}"的训练记录` : '请先选择一个汉字' }}</text>
        </view>
        <view v-else class="record-popup-list">
          <view v-for="record in filteredRecords" :key="record._id"
            :class="['record-popup-item', { active: record.character === currentWord }]"
            @click="handleSelectRecord(record)">
            <view class="record-popup-item-main">
              <view class="record-popup-item-header">
                <text class="record-popup-character">{{ record.character }}</text>
                <text class="record-popup-date">{{ formatDateForRecord(record.createTime) }}</text>
              </view>
            </view>
            <view class="record-popup-item-delete" @click.stop="handleDeleteRecord(record._id)">
              <i class="fas fa-trash-alt"></i>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick, onUnmounted } from 'vue'
import ZMessageInput from '@/components/z-message-input/z-message-input.vue'
import ZAudioPlayer from '@/components/z-audio-player/z-audio-player.vue'
import ZPageNavbar from '@/components/z-page-navbar.vue'
import ZLoading from '@/components/z-loading/index.vue'
import { generateWordsAndPhrasesFromChar } from '@/api/speak'
import { addChatRecordApi, getChatRecordListApi, updateChatRecordApi, delChatRecordApi } from '@/api/chatRecord'

console.log('[DEBUG] 普通话训练页面脚本开始加载：', new Date().toISOString())

// 云对象导入
const speakObj = uniCloud.importObject('speak')

// 本地缓存所有普通话训练数据
let allPutonghuaData = []

/**
 * 从云数据库加载普通话训练数据
 * 页面初始化时一次性获取所有普通话训练数据
 * 实现本地缓存机制，减少重复网络请求
 */
const loadPutonghuaData = async (forceRefresh = false) => {
  const CACHE_KEY = 'putonghua_training_data'
  const CACHE_TIME_KEY = 'putonghua_training_data_time'
  const CACHE_DURATION = 24 * 60 * 60 * 1000 // 24小时缓存有效期

  isLoadingPutonghuaData.value = true
  putonghuaDataError.value = ''

  try {
    // 检查本地缓存
    if (!forceRefresh) {
      const cachedData = uni.getStorageSync(CACHE_KEY)
      const cacheTime = uni.getStorageSync(CACHE_TIME_KEY)
      const now = Date.now()

      if (cachedData && cacheTime && (now - cacheTime < CACHE_DURATION)) {
        console.log('[DEBUG] 使用本地缓存的普通话数据，缓存时间：', new Date(cacheTime))
        allPutonghuaData = cachedData
        hasCloudData.value = true
        return cachedData
      }
    }

    console.log('[DEBUG] 开始从云数据库加载普通话数据')
    const result = await speakObj.getPutonghuaData()
    console.log('[DEBUG] 云数据库返回结果：', result)

    if (result.code === 0) {
      allPutonghuaData = result.data // 缓存到内存
      hasCloudData.value = result.data && result.data.length > 0

      // 保存到本地存储
      try {
        uni.setStorageSync(CACHE_KEY, result.data)
        uni.setStorageSync(CACHE_TIME_KEY, Date.now())
        console.log('[DEBUG] 普通话数据已缓存到本地存储')
      } catch (storageError) {
        console.warn('[WARN] 本地存储缓存失败：', storageError)
      }

      console.log('[DEBUG] 普通话数据加载成功，数据条数：', allPutonghuaData.length)
      return result.data
    } else {
      throw new Error(result.message || '数据加载失败')
    }
  } catch (error) {
    console.error('[ERROR] 获取普通话数据失败：', error)
    putonghuaDataError.value = error.message || '数据加载失败'

    // 网络失败时尝试使用本地缓存
    const cachedData = uni.getStorageSync(CACHE_KEY)
    if (cachedData && cachedData.length > 0) {
      console.log('[DEBUG] 网络失败，使用本地缓存数据')
      allPutonghuaData = cachedData
      hasCloudData.value = true
      putonghuaDataError.value = '网络异常，使用离线数据'
      uni.showToast({ title: '使用离线数据', icon: 'none' })
      return cachedData
    }

    hasCloudData.value = false
    uni.showToast({ title: '数据加载失败', icon: 'none' })
    return []
  } finally {
    isLoadingPutonghuaData.value = false
  }
}

/**
 * 根据汉字从本地缓存中获取训练数据
 */
const getPutonghuaByCharacter = (character) => {
  return allPutonghuaData.find(item => item.character === character) || null
}

/**
 * 动态生成分类并按分类组织数据
 */
const organizeByCategory = (dataList) => {
  const categories = {}

  // 自动收集所有分类并去重
  dataList.forEach(data => {
    const category = data.category || '未分类'
    if (!categories[category]) {
      categories[category] = []
    }
    categories[category].push(data.character)
  })

  // 对每个分类内的汉字进行排序
  Object.keys(categories).forEach(category => {
    categories[category].sort((a, b) => a.localeCompare(b, 'zh-Hans-CN'))
  })

  console.log('[DEBUG] 动态生成的分类：', categories)
  return categories
}

/**
 * 随机选择词语和句子（本地操作）
 */
const getRandomContent = (character) => {
  const characterData = getPutonghuaByCharacter(character)
  if (!characterData || !characterData.words || !characterData.words.length) {
    console.log('[DEBUG] 没有找到字符数据或词语数据：', character)
    return null
  }

  // 随机选择一个词语
  const randomWord = characterData.words[Math.floor(Math.random() * characterData.words.length)]

  // 随机选择该词语的一个句子
  const randomSentence = randomWord.sentences && randomWord.sentences.length > 0
    ? randomWord.sentences[Math.floor(Math.random() * randomWord.sentences.length)]
    : null

  console.log('[DEBUG] 随机选择的内容：', { character, word: randomWord, sentence: randomSentence })

  return {
    character: characterData.character,
    word: randomWord,
    sentence: randomSentence
  }
}

/**
 * 清除普通话数据缓存
 */
const clearPutonghuaCache = () => {
  try {
    uni.removeStorageSync('putonghua_training_data')
    uni.removeStorageSync('putonghua_training_data_time')
    allPutonghuaData = []
    console.log('[DEBUG] 普通话数据缓存已清除')
  } catch (error) {
    console.error('[ERROR] 清除缓存失败：', error)
  }
}

/**
 * 重试加载数据
 */
const retryLoadData = async () => {
  console.log('[DEBUG] 用户点击重试加载数据')
  try {
    const putonghuaData = await loadPutonghuaData(true) // 强制刷新

    if (putonghuaData && putonghuaData.length > 0) {
      // 重新生成字库
      wordBank.value = organizeByCategory(putonghuaData)

      // 如果当前有选中的字符，重新生成内容
      if (currentWord.value) {
        await generateContentForCharacter(currentWord.value)
      }

      uni.showToast({ title: '数据加载成功', icon: 'success' })
    }
  } catch (error) {
    console.error('[ERROR] 重试加载失败：', error)
    uni.showToast({ title: '重试失败', icon: 'none' })
  }
}

const getHistory = async () => {
  try {
    const allRecords = await getChatRecordListApi()
    const characterRecords = allRecords
      .filter((r) => {
        try {
          if (r.content && typeof r.content === 'string' && r.content.trim().startsWith('{')) {
            const content = JSON.parse(r.content)
            return content.type === 'putonghuaCharacter'
          }
          return false
        } catch {
          return false
        }
      })
      .map((r) => {
        try {
          const content = JSON.parse(r.content)
          return {
            character: r.title,
            category: content.category || '未分类',
          }
        } catch {
          return { character: r.title, category: '未分类' }
        }
      })

    // 去重
    const uniqueRecords = characterRecords.reduce((acc, current) => {
      if (!acc.some((item) => item.character === current.character)) {
        acc.push(current)
      }
      return acc
    }, [])

    // 按分类分组
    const grouped = uniqueRecords.reduce((acc, record) => {
      const { category, character } = record
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(character)
      return acc
    }, {})

    // 对每个分类下的汉字排序
    for (const category in grouped) {
      grouped[category].sort((a, b) => a.localeCompare(b, 'zh-Hans-CN'))
    }

    return grouped
  } catch (error) {
    console.error('Failed to fetch history tags', error)
    uni.showToast({ title: '加载字库失败', icon: 'none' })
    return {}
  }
}

const addHistory = async (word, category) => {
  const allWords = Object.values(wordBank.value).flat()
  if (allWords.includes(word)) {
    return { success: false, message: '该字已在您的字库中' }
  }
  try {
    await addChatRecordApi({
      title: word,
      content: JSON.stringify({ type: 'putonghuaCharacter', category }),
    })
    return { success: true }
  } catch (error) {
    console.error('Failed to add history tag:', error)
    return { success: false, message: '添加失败，请重试' }
  }
}

const isWordBankCollapsed = ref(false)

console.log('[DEBUG] 开始初始化响应式数据')

const showPinyin = ref(true)
console.log('[DEBUG] showPinyin 初始化完成：', showPinyin.value)

const wordBank = ref({})
console.log('[DEBUG] wordBank 初始化完成：', wordBank.value)

const currentWord = ref('')
console.log('[DEBUG] currentWord 初始化完成：', currentWord.value)

const contentLoading = ref(false)
console.log('[DEBUG] contentLoading 初始化完成：', contentLoading.value)

const mockAudioUrl = ref('https://downsc.chinaz.net/Files/DownLoad/sound1/201906/11582.mp3') // 模拟音频

const generatedContent = ref({
  wordsAndPhrases: [],
  sentences: [],
  paragraph: null,
})
console.log('[DEBUG] generatedContent 初始化完成：', JSON.stringify(generatedContent.value))

// 添加日志监控 generatedContent 的变化
watch(
  generatedContent,
  (newVal, oldVal) => {
    console.log('[DEBUG] generatedContent 变化：', {
      newVal: JSON.stringify(newVal),
      oldVal: JSON.stringify(oldVal),
      timestamp: new Date().toISOString(),
    })
  },
  { deep: true }
)

// 添加单字弹窗相关
const showAddModal = ref(false)
const newTagInput = ref('')
const categories = ref(['平舌音', '翘舌音', '前鼻音', '后鼻音'])
const selectedCategory = ref('')

// 训练记录相关
const recordListPopupRef = ref(null)
const showRecordListPopup = ref(false)
const records = ref([])
const isLoadingRecords = ref(false)

// 录音相关状态
const inputMessage = ref('')
const currentAudioURL = ref('')
const isPlaying = ref(false)
const audioPlayerRef = ref(null)
const currentRecordId = ref(null) // 当前录音记录的 ID
const isTranscribing = ref(false)
const transcriptionText = ref('')
const currentAudioData = ref(null)
const currentSentences = ref(null)

// 数据加载状态管理
const isLoadingPutonghuaData = ref(false)
const putonghuaDataError = ref('')
const hasCloudData = ref(false)

const openAddModal = () => {
  console.log('ccccc')
  newTagInput.value = ''
  selectedCategory.value = ''
  showAddModal.value = true
}

const closeAddModal = () => {
  showAddModal.value = false
}

const addHistoryTag = async () => {
  if (!newTagInput.value || !/^[\u4e00-\u9fa5]$/.test(newTagInput.value)) {
    uni.showToast({ title: '请输入单个汉字', icon: 'none' })
    return
  }
  if (!selectedCategory.value) {
    uni.showToast({ title: '请选择一个分类', icon: 'none' })
    return
  }

  try {
    const result = await addHistory(newTagInput.value, selectedCategory.value)
    if (result.success) {
      const newWord = newTagInput.value
      const category = selectedCategory.value

      if (!wordBank.value[category]) {
        wordBank.value[category] = []
      }
      wordBank.value[category].push(newWord)
      wordBank.value[category].sort((a, b) => a.localeCompare(b, 'zh-Hans-CN'))

      handleTagClick(newWord) // 添加成功后自动选中
    } else {
      uni.showToast({ title: result.message || '添加失败，字已存在', icon: 'none' })
    }
  } finally {
    closeAddModal()
  }
}

// 修改后的单字点击逻辑：切换到该字的最新训练记录
const handleTagClick = async (tag) => {
  console.log('[DEBUG] handleTagClick 开始：', { tag, timestamp: new Date().toISOString() })

  currentWord.value = tag
  isWordBankCollapsed.value = true // 选择后自动收起
  uni.setStorageSync('lastSelectedPinyinChar', tag)
  contentLoading.value = true

  // 清空当前内容
  generatedContent.value = { wordsAndPhrases: [], sentences: [], paragraph: null }
  currentAudioURL.value = ''
  transcriptionText.value = ''
  currentSentences.value = null

  try {
    // 查找该字符的最新训练记录
    const latestRecord = parsedRecords.value.find(
      (r) => r.character === tag && r.recordType === 'putonghuaTraining'
    )

    if (latestRecord) {
      console.log('[DEBUG] 找到历史记录，加载历史内容：', latestRecord._id)
      await handleSelectRecord(latestRecord)
    } else {
      console.log('[DEBUG] 没有历史记录，自动生成新内容')
      await generateContentForCharacter(tag)
    }
  } catch (error) {
    console.error('[ERROR] handleTagClick 失败：', error)
    uni.showToast({ title: '加载失败', icon: 'none' })
  } finally {
    contentLoading.value = false
    console.log('[DEBUG] handleTagClick 完成：', { tag, timestamp: new Date().toISOString() })
  }
}

// 重新生成内容的函数
const handleRegenerateContent = async () => {
  if (!currentWord.value) return

  console.log('[DEBUG] handleRegenerateContent 开始：', currentWord.value)
  contentLoading.value = true

  try {
    // 重新生成时，可以选择强制刷新云数据库缓存
    // 这里先尝试从当前缓存中重新随机选择
    await generateContentForCharacter(currentWord.value)
    uni.showToast({ title: '重新生成成功', icon: 'success' })
  } catch (error) {
    console.error('[ERROR] handleRegenerateContent 失败：', error)
    uni.showToast({ title: '重新生成失败', icon: 'none' })
  } finally {
    contentLoading.value = false
  }
}

// 为指定字符生成内容的通用函数
const generateContentForCharacter = async (character) => {
  console.log('[DEBUG] generateContentForCharacter 开始：', character)

  // 清空当前内容
  generatedContent.value = { wordsAndPhrases: [], sentences: [], paragraph: null }
  currentAudioURL.value = ''
  transcriptionText.value = ''
  currentSentences.value = null

  // 优先从云数据库获取数据
  const randomContent = getRandomContent(character)

  if (randomContent) {
    console.log('[DEBUG] 使用云数据库数据生成内容')

    // 构建词语和短语数据
    const wordsAndPhrases = []
    if (randomContent.word) {
      wordsAndPhrases.push({
        text: randomContent.word.text,
        pinyin: randomContent.word.pinyin
      })
    }

    // 构建句子数据
    const sentences = []
    if (randomContent.sentence) {
      sentences.push({
        text: randomContent.sentence.text,
        pinyin: randomContent.sentence.pinyin
      })
    }

    // 设置生成的内容
    generatedContent.value.wordsAndPhrases = wordsAndPhrases
    generatedContent.value.sentences = sentences
    generatedContent.value.paragraph = null // 云数据库暂不支持段落

    console.log('[DEBUG] 云数据库内容设置完成：', JSON.stringify(generatedContent.value))
  } else {
    console.log('[DEBUG] 云数据库无数据，回退到API生成')

    // 回退到原有的API调用
    const result = await generateWordsAndPhrasesFromChar(character)
    console.log('[DEBUG] generateWordsAndPhrasesFromChar 返回结果：', JSON.stringify(result))

    // 设置生成的内容
    generatedContent.value.wordsAndPhrases = result.wordsAndPhrases || []
    generatedContent.value.sentences = result.sentences || []
    generatedContent.value.paragraph = result.paragraph || null
  }

  // 加载已存在的录音
  await loadExistingRecording(character)

  // 保存记录
  await saveRecord()
}

// 加载已存在的录音记录
const loadExistingRecording = async (character) => {
  try {
    // 查找该字符的录音记录
    const trainingRecord = parsedRecords.value.find(
      (r) => r.character === character && r.recordType === 'putonghuaTraining'
    )

    if (trainingRecord && trainingRecord.audioURL) {
      currentAudioURL.value = trainingRecord.audioURL
      transcriptionText.value = trainingRecord.transcript || ''
      currentSentences.value = trainingRecord.sentences || null
      console.log('已加载录音记录：', trainingRecord.audioURL)
    } else {
      currentAudioURL.value = ''
      transcriptionText.value = ''
      currentSentences.value = null
    }
  } catch (error) {
    console.error('加载录音记录失败：', error)
  }
}

onMounted(async () => {
  console.log('[DEBUG] onMounted 开始：', new Date().toISOString())

  try {
    // 1. 首先加载云数据库的普通话训练数据
    console.log('[DEBUG] 开始加载云数据库数据')
    const putonghuaData = await loadPutonghuaData()

    // 2. 从云数据库数据生成动态分类字库
    if (putonghuaData && putonghuaData.length > 0) {
      console.log('[DEBUG] 使用云数据库数据生成字库')
      wordBank.value = organizeByCategory(putonghuaData)
      console.log('[DEBUG] 云数据库字库生成完成，分类数量：', Object.keys(wordBank.value).length)
    } else {
      console.log('[DEBUG] 云数据库无数据，回退到历史字库')
      // 回退到原有的字库获取逻辑
      wordBank.value = await getHistory()
      console.log('[DEBUG] 历史字库获取完成，标签数量：', Object.keys(wordBank.value).length)
    }

    // 3. 获取训练记录
    console.log('[DEBUG] 开始获取训练记录')
    await fetchRecords()
    console.log('[DEBUG] 训练记录获取完成，记录数量：', records.value.length)

    // 4. 优先选择最新的训练记录
    if (parsedRecords.value.length > 0) {
      console.log('[DEBUG] 找到训练记录，选择最新记录')
      const latestRecord = parsedRecords.value[0] // 已按时间倒序排列
      console.log('[DEBUG] 最新记录：', JSON.stringify(latestRecord))
      handleSelectRecord(latestRecord)
      return
    }

    // 5. 如果没有训练记录，按字库逻辑处理
    if (Object.keys(wordBank.value).length > 0) {
      console.log('[DEBUG] 没有训练记录，使用字库逻辑')
      const lastSelectedWord = uni.getStorageSync('lastSelectedPinyinChar')
      console.log('[DEBUG] 上次选择的字符：', lastSelectedWord)

      const allWords = Object.values(wordBank.value).flat()
      if (lastSelectedWord && allWords.includes(lastSelectedWord)) {
        console.log('[DEBUG] 使用上次选择的字符：', lastSelectedWord)
        handleTagClick(lastSelectedWord)
      } else {
        const firstCategory = Object.keys(wordBank.value)[0]
        if (wordBank.value[firstCategory]?.length > 0) {
          console.log('[DEBUG] 使用第一个字符：', wordBank.value[firstCategory][0])
          handleTagClick(wordBank.value[firstCategory][0])
        }
      }
    } else {
      console.log('[DEBUG] 没有字库数据')
    }
  } catch (error) {
    console.error('[ERROR] onMounted 失败：', error)
  }

  console.log('[DEBUG] onMounted 完成：', new Date().toISOString())
})

// --- 训练记录逻辑 ---

const fetchRecords = async () => {
  isLoadingRecords.value = true
  try {
    const allRecords = await getChatRecordListApi()
    records.value = allRecords
      .filter((r) => {
        try {
          if (typeof r.content === 'string' && r.content.trim().startsWith('{')) {
            const content = JSON.parse(r.content)
            // 只保留 putonghuaTraining 记录
            return content.type === 'putonghuaTraining'
          }
          return false
        } catch {
          return false
        }
      })
      .sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
  } catch (error) {
    console.error('Failed to fetch records', error)
    records.value = []
  } finally {
    isLoadingRecords.value = false
  }
}

watch(showRecordListPopup, (newValue) => {
  if (newValue) {
    fetchRecords()
    recordListPopupRef.value?.open()
  } else {
    recordListPopupRef.value?.close()
  }
})

const handleRecordListPopupChange = (e) => {
  if (!e.show) {
    showRecordListPopup.value = false
  }
}

const closeRecordListPopup = () => {
  showRecordListPopup.value = false
}

// 显示训练记录列表
const handleShowRecordList = () => {
  if (!currentWord.value) {
    uni.showToast({ title: '请先选择一个汉字', icon: 'none' })
    return
  }
  showRecordListPopup.value = true
}

// 清空当前字符的训练记录
const handleClearCurrentRecords = async () => {
  if (!currentWord.value || filteredRecords.value.length === 0) return

  try {
    const recordIdsToDelete = filteredRecords.value.map(r => r._id)
    await Promise.all(recordIdsToDelete.map(id => delChatRecordApi(id)))
    uni.showToast({ title: `已清空"${currentWord.value}"的训练记录`, icon: 'success' })

    // 更新记录列表
    records.value = records.value.filter(r => !recordIdsToDelete.includes(r._id))

    // 如果清空的是当前显示的记录，重新生成内容
    if (filteredRecords.value.length === 0) {
      await generateContentForCharacter(currentWord.value)
    }

    // 关闭弹窗
    showRecordListPopup.value = false
  } catch (error) {
    console.error('清空记录失败：', error)
    uni.showToast({ title: '清空失败', icon: 'none' })
  }
}

const parsedRecords = computed(() => {
  console.log('[DEBUG] parsedRecords 计算开始，原始记录数量：', records.value?.length || 0)

  if (!records.value || records.value.length === 0) {
    console.log('[DEBUG] parsedRecords: 没有原始记录')
    return []
  }

  const parsed = records.value
    .map((record, index) => {
      try {
        console.log(`[DEBUG] 解析记录 ${index}:`, JSON.stringify(record))
        const content = JSON.parse(record.content)
        console.log(`[DEBUG] 解析内容 ${index}:`, JSON.stringify(content))

        const result = {
          _id: record._id,
          createTime: record.createTime,
          character: content.character,
          generatedContent: content.content,
          audioURL: content.audioURL || '', // 添加录音 URL 支持
          recordType: content.type, // 记录类型
          transcript: content.transcript || '', // 解析转写文本
          sentences: content.sentences || null,
        }

        console.log(`[DEBUG] 解析结果 ${index}:`, JSON.stringify(result))
        return result
      } catch (e) {
        console.error(`[ERROR] 解析记录 ${index} 失败:`, e, record)
        return null
      }
    })
    .filter(Boolean)

  console.log('[DEBUG] parsedRecords 计算完成，解析记录数量：', parsed.length)
  return parsed
})

// 筛选当前字符的训练记录
const filteredRecords = computed(() => {
  if (!currentWord.value) {
    return []
  }

  return parsedRecords.value.filter(record =>
    record.character === currentWord.value && record.recordType === 'putonghuaTraining'
  )
})

const formatDateForRecord = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(
    2,
    '0'
  )} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

const handleSelectRecord = async (selectedRecord) => {
  console.log('[DEBUG] handleSelectRecord 开始：', JSON.stringify(selectedRecord))

  currentWord.value = selectedRecord.character
  isWordBankCollapsed.value = true // 从记录选择后也收起字库
  console.log('[DEBUG] 设置 currentWord:', selectedRecord.character)

  console.log('[DEBUG] 设置 generatedContent 前：', JSON.stringify(generatedContent.value))
  generatedContent.value = selectedRecord.generatedContent
  console.log('[DEBUG] 设置 generatedContent 后：', JSON.stringify(generatedContent.value))

  // 恢复录音数据（如果存在）
  currentAudioURL.value = selectedRecord.audioURL || ''
  transcriptionText.value = selectedRecord.transcript || ''
  currentSentences.value = selectedRecord.sentences || null

  // 更新本地存储，保持与字库选择的一致性
  uni.setStorageSync('lastSelectedPinyinChar', selectedRecord.character)

  // 刷新字库以确保新添加的字可见
  wordBank.value = await getHistory()

  closeRecordListPopup()
  console.log('[DEBUG] handleSelectRecord 完成')
}

const handleDeleteRecord = async (recordId) => {
  try {
    await delChatRecordApi(recordId)
    uni.showToast({ title: '删除成功', icon: 'none' })
    records.value = records.value.filter((r) => r._id !== recordId)
    if (parsedRecords.value.length === 0) {
      handleReset()
    }
  } catch (error) {
    console.error('Failed to delete record:', error)
    uni.showToast({ title: '删除失败', icon: 'none' })
  }
}

const handleClearAllRecords = async () => {
  if (parsedRecords.value.length === 0) return
  isLoadingRecords.value = true
  try {
    const recordIdsToDelete = parsedRecords.value.map((r) => r._id)
    await Promise.all(recordIdsToDelete.map((id) => delChatRecordApi(id)))
    uni.showToast({ title: '已清空所有记录', icon: 'none' })
    records.value = []
    handleReset()
  } catch (error) {
    console.error('Failed to clear records:', error)
    uni.showToast({ title: '清空失败', icon: 'none' })
  } finally {
    isLoadingRecords.value = false
  }
}

const saveRecord = async () => {
  if (!currentWord.value) return

  const recordPayload = {
    title: currentWord.value,
    content: JSON.stringify({
      type: 'putonghuaTraining',
      character: currentWord.value,
      content: generatedContent.value,
      // 当有新录音时，使用新的数据，否则保留旧的
      audioURL: currentAudioData.value?.tempFileURL || currentAudioURL.value,
      transcript: transcriptionText.value,
      sentences: currentSentences.value,
      fileID: currentAudioData.value?.fileID,
      duration: currentAudioData.value?.duration,
    }),
  }

  try {
    const existingRecord = parsedRecords.value.find(
      (r) => r.character === currentWord.value && r.recordType === 'putonghuaTraining'
    )
    if (existingRecord) {
      await updateChatRecordApi(existingRecord._id, recordPayload)
      currentRecordId.value = existingRecord._id
    } else {
      const newId = await addChatRecordApi(recordPayload)
      currentRecordId.value = newId
    }
    await fetchRecords() // Refresh the list after saving
  } catch (error) {
    console.error('Failed to save record:', error)
  }
}

// 录音相关方法
const handleAudioSubmit = async (audioData) => {
  console.log('收到录音数据', audioData)

  if (audioData && audioData.tempFileURL) {
    currentAudioURL.value = audioData.tempFileURL
    currentAudioData.value = audioData // 暂存音频数据，用于转写后保存

    // 使用 nextTick 确保 DOM 更新后，player 组件的 ref 可用
    nextTick(() => {
      if (audioPlayerRef.value) {
        // 显式调用子组件的转写方法
        audioPlayerRef.value.transcribe()
      }
    })
  }
}

/*
const saveRecordingData = async (audioData, transcript = '') => {
  if (!currentWord.value) return

  const recordPayload = {
    title: `${currentWord.value}_录音练习`,
    content: JSON.stringify({
      type: 'putonghuaRecording',
      character: currentWord.value,
      audioURL: audioData.tempFileURL,
      fileID: audioData.fileID,
      duration: audioData.duration,
      transcript: transcript, // 保存转写结果
      timestamp: new Date().toISOString(),
    }),
  }

  try {
    // 查找是否已存在该字符的录音记录
    const existingRecordingRecord = parsedRecords.value.find((r) => {
      try {
        const content = JSON.parse(r.content || '{}')
        return content.type === 'putonghuaRecording' && content.character === currentWord.value
      } catch {
        return false
      }
    })

    if (existingRecordingRecord) {
      // 更新现有录音记录（单条记录策略）
      await updateChatRecordApi(existingRecordingRecord._id, recordPayload)
      console.log('录音记录已更新：', existingRecordingRecord._id)
    } else {
      // 创建新的录音记录
      const newId = await addChatRecordApi(recordPayload)
      console.log('新录音记录已保存：', newId)
    }

    await fetchRecords() // 刷新记录列表
  } catch (error) {
    console.error('保存录音记录失败：', error)
    uni.showToast({
      title: '保存录音失败',
      icon: 'none',
    })
  }
}
*/

const handleUploadProgress = (progress) => {
  console.log('上传进度', progress)
}

const handleRecordError = (error) => {
  console.error('录音错误', error)
  uni.showToast({
    title: '录音出错，请重试',
    icon: 'none',
    duration: 3000,
  })
}

// 转写事件处理
const handleTranscriptionStart = () => {
  console.log('开始转写')
  isTranscribing.value = true
  transcriptionText.value = ''
}

const handleTranscriptionEnd = async (result) => {
  console.log('转写结束', result)
  isTranscribing.value = false

  if (result.success && result.transcript) {
    transcriptionText.value = result.transcript
    currentSentences.value = result.sentences
    uni.showToast({
      title: '识别成功',
      icon: 'success',
    })
    // 保存包含转写结果的录音记录
    if (currentAudioData.value) {
      await saveRecord()
      currentAudioData.value = null // 清理临时数据
    }
  } else {
    uni.showToast({
      title: '识别失败，请重试',
      icon: 'none',
    })
    // 即使转写失败，也保存录音文件
    if (currentAudioData.value) {
      transcriptionText.value = '' // 确保转写文本为空
      currentSentences.value = null
      await saveRecord()
      currentAudioData.value = null // 清理临时数据
    }
  }
}

// 音频播放器事件处理
const onAudioPlay = () => {
  isPlaying.value = true
}

const onAudioPause = () => {
  isPlaying.value = false
}

const onAudioEnded = () => {
  isPlaying.value = false
}

const handleReset = () => {
  currentWord.value = ''
  generatedContent.value = { wordsAndPhrases: [], sentences: [], paragraph: null }
  wordBank.value = {}
  records.value = []

  // 清理录音相关状态
  currentAudioURL.value = ''
  inputMessage.value = ''
  currentRecordId.value = null
  transcriptionText.value = ''
  currentSentences.value = null
  isTranscribing.value = false
  currentAudioData.value = null

  // 停止任何正在播放的音频
  if (audioPlayerRef.value) {
    audioPlayerRef.value.pause()
  }
  isPlaying.value = false
}

// 组件卸载时清理资源
onUnmounted(() => {
  if (currentAudioURL.value && currentAudioURL.value.startsWith('blob:')) {
    URL.revokeObjectURL(currentAudioURL.value)
  }

  // 停止音频播放
  if (audioPlayerRef.value) {
    audioPlayerRef.value.pause()
  }
})

// 文字和拼音对齐处理函数
const splitTextIntoCharacters = (text) => {
  console.log('[DEBUG] splitTextIntoCharacters 输入：', text)
  if (!text) {
    console.log('[DEBUG] splitTextIntoCharacters: 文本为空')
    return []
  }
  // 将文本分割成单个字符，包括汉字、标点符号等
  const result = text.split('')
  console.log('[DEBUG] splitTextIntoCharacters 结果：', result)
  return result
}

const getPinyinForCharacter = (pinyinString, charIndex) => {
  console.log('[DEBUG] getPinyinForCharacter 输入：', { pinyinString, charIndex })
  if (!pinyinString) {
    console.log('[DEBUG] getPinyinForCharacter: 拼音字符串为空')
    return ''
  }

  // 将拼音字符串按空格分割
  const pinyinArray = pinyinString.trim().split(/\s+/)
  console.log('[DEBUG] getPinyinForCharacter 拼音数组：', pinyinArray)

  // 返回对应位置的拼音，如果超出范围则返回空字符串
  const result = pinyinArray[charIndex] || ''
  console.log('[DEBUG] getPinyinForCharacter 结果：', result)
  return result
}
</script>

<style lang="scss" scoped>
.training-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-right: 5px;

  .action-btn {
    font-size: 18px;
    color: var(--color-text-secondary);
    cursor: pointer;

    &:hover {
      color: var(--color-primary);
    }
  }
}

.navbar-pinyin-toggle {
  font-size: 14px;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  padding: 4px 8px;
  border-radius: var(--rounded-md);
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: var(--color-primary-light-9);
  }
}

.word-bank-section {
  background-color: #fff;
  margin: 15px;
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  .word-bank-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .word-bank-title-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .current-word-display {
    background-color: var(--color-primary-light-9);
    color: var(--color-primary);
    padding: 3px 8px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 8px;

    .regenerate-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      border-radius: 6px;
      background-color: var(--color-primary-light-9);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--color-primary-light-8);
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }

      i {
        font-size: 12px;
        color: var(--color-primary);
      }
    }

    .collapse-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--color-primary-light-9);
        border-radius: 6px;
      }

      i {
        font-size: 14px;
        color: #909399;
        transition: transform 0.3s ease;
      }
    }
  }

  &.collapsed .word-bank-content {
    display: none;
  }

  &:not(.collapsed) .word-bank-header {
    margin-bottom: 15px;
  }

  .word-bank-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 0;
  }
}

.word-bank-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}

.category-group {
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  background-color: #f8f9fa;
  padding: 5px 10px;
  border-radius: 8px;
}

.category-name-tag {
  background-color: #e9ecef;
  color: #495057;
  border-radius: 6px;
  padding: 8px 10px;
  font-size: 14px;
  font-weight: 500;
}

.history-tag {
  background-color: var(--color-primary-light-9);
  color: var(--color-primary);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  &.active {
    background-color: var(--color-primary);
    color: #fff;
    font-weight: bold;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(41, 121, 255, 0.4);
  }
}

.add-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 8px;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  cursor: pointer;
  transition: all 0.2s;

  i {
    font-size: 16px;
    color: #909399;
  }

  &:hover {
    border-color: var(--color-primary);
    background-color: var(--color-primary-light-9);

    i {
      color: var(--color-primary);
    }
  }
}

.content-section {
  flex: 1;
  overflow-y: auto;
  padding: 0 15px 100px 15px; // 增加底部内边距为录音输入区域留空间
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 200px;
}

// 数据加载状态样式
.data-loading-hint {
  width: 100%;
  text-align: center;
  padding: 40px 20px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.data-error-hint {
  width: 100%;
  text-align: center;
  padding: 40px 20px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-left: 4px solid var(--color-danger, #f56c6c);

  .error-icon {
    margin-bottom: 15px;

    i {
      font-size: 32px;
      color: var(--color-danger, #f56c6c);
    }
  }

  .error-text {
    display: block;
    color: var(--color-text-secondary);
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.5;
  }

  .error-actions {
    display: flex;
    justify-content: center;
  }

  .retry-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: var(--color-primary);
    color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;

    &:hover {
      background-color: var(--color-primary-dark-1);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    i {
      font-size: 12px;
    }
  }
}

.data-warning-hint {
  width: 100%;
  text-align: center;
  padding: 12px 20px;
  background-color: var(--color-warning-light-9, #fdf6ec);
  border-radius: 6px;
  margin-bottom: 15px;
  border-left: 4px solid var(--color-warning, #e6a23c);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  .warning-icon {
    i {
      font-size: 16px;
      color: var(--color-warning, #e6a23c);
    }
  }

  .warning-text {
    color: var(--color-warning-dark-2, #b88230);
    font-size: 13px;
    line-height: 1.4;
  }
}
.content-block {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  width: 100%;

  .content-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 12px;

    i {
      color: var(--color-primary);
      margin-right: 8px;
    }
  }

  // 文字和拼音对齐样式
  .text-pinyin-container {
    display: inline-block;
    width: 100%;
  }

  .character-row {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    gap: 2px;
  }

  .character-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    min-width: 24px;
    margin: 0 1px;
    position: relative;
  }

  .pinyin-text {
    font-size: 10px;
    color: #888;
    line-height: 1.2;
    text-align: center;
    white-space: nowrap;
    font-weight: normal;
    margin-bottom: 2px;
    min-height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .chinese-text {
    font-size: 18px;
    line-height: 1.4;
    color: #333;
    text-align: center;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 25px;
  }
}

.phrase-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

.phrase-item,
.sentence-item {
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-block;
  margin: 4px;

  &:hover {
    background-color: #e9ecf0;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.sentence-item {
  display: block;
  width: 100%;
  margin: 8px 0;

  .character-row {
    justify-content: flex-start;
  }
}

.paragraph-item {
  display: block;
  width: 100%;
  margin: 8px 0;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 16px 20px;
  border-left: 4px solid var(--color-primary);

  .character-row {
    justify-content: flex-start;
    line-height: 1.8;
  }

  .text-pinyin-container {
    width: 100%;
  }
}
// 录音练习区域样式
.recording-practice-block {
  margin-bottom: 20px;
  border: 2px solid var(--color-primary-light-8);
  background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);

  .content-title {
    color: var(--color-primary);
    font-weight: 600;

    i {
      color: var(--color-primary);
    }
  }
}

.audio-player-section {
  margin-top: 15px;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid var(--color-primary-light-7);
}

.practice-audio-player {
  width: 100%;
}

.recording-hint {
  text-align: center;
  padding: 20px;
  color: var(--color-text-secondary);
  font-size: 15px;
  font-style: italic;

  text {
    color: var(--color-primary);
    font-weight: 500;
  }
}

// 录音输入区域样式
.recording-input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-white, #fff);
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.06);
  z-index: 100;
  border-top: 1px solid var(--color-border, #e9e9e9);
}
.audio-player-placeholder {
  padding: 0 15px 10px;
}

.recording-section {
  border-top: 1px solid #e0e0e0;
}

.modal-content {
  padding: 15px;
}

.category-selector {
  margin-top: 20px;
}
.category-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 12px;
}
.category-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.category-tag {
  background-color: #f3f4f6;
  color: #606266;
  padding: 6px 15px;
  border-radius: 20px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}
.category-tag:hover {
  opacity: 0.8;
}
.category-tag.active {
  background-color: var(--color-primary);
  color: white;
  font-weight: 500;
}

/* 训练记录弹窗样式 */
.record-popup-content {
  background-color: white;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.record-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 1;
}
.record-popup-title {
  font-size: 16px;
  font-weight: bold;
}
.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}
.clear-all-btn {
  font-size: 14px;
  color: var(--color-danger, #f56c6c);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}
.clear-all-btn:hover {
  background-color: var(--color-danger-light-2, #fde2e2);
}
.record-popup-close-icon {
  cursor: pointer;
}
.record-popup-scroll-view {
  height: 50vh;
}
.record-popup-empty-state {
  text-align: center;
  padding: 40px;
  color: #999;
}
.record-popup-list {
  padding: 0 15px;
}
.record-popup-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 5px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}
.record-popup-item.active {
  background-color: var(--color-primary-light-9);
}
.record-popup-item:hover {
  background-color: #f5f5f5;
}
.record-popup-item:last-child {
  border-bottom: none;
}
.record-popup-item-main {
  flex: 1;
  min-width: 0;
}
.record-popup-item-delete {
  padding: 8px;
  color: #999;
  border-radius: 50%;
  transition: all 0.2s ease;
}
.record-popup-item-delete:hover {
  background-color: #fde2e2;
  color: #f56c6c;
}
.record-popup-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.record-popup-date {
  font-size: 12px;
  color: #999;
}
.record-popup-character {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
</style>
