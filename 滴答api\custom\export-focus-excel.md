# 导出专注记录到Excel

将用户的所有专注记录导出为Excel文件，包含完整的专注时间线数据。

## 接口信息

- **接口URL**: `http://localhost:8000/custom/export/focus/excel`
- **请求方法**: `GET`
- **认证要求**: 需要登录认证
- **所属平台**: 本项目自定义接口

## 功能说明

此接口会将用户的所有专注记录数据导出为Excel文件，包含以下工作表：

### 工作表：专注记录时间线
包含所有专注记录的详细信息，数据来源于 `/api/v2/pomodoros/timeline` 接口，支持自动分页获取所有历史数据。

## 导出字段

采用紧凑型展示方式，每行一个专注会话，通过特殊格式直观展示专注和暂停模式：

### 主要字段

- **会话ID**: 专注会话的唯一标识符
- **会话时间**: 会话的日期和时间范围，格式：`2025-06-05 11:44 - 17:33`
- **总时长**: 会话总时长，格式：`5小时49分钟`
- **暂停时长**: 会话中的暂停时长，格式：`1小时11分钟`
- **任务标题**: 专注的任务标题（多个任务用分号分隔）
- **项目**: 任务所属项目（多个项目用分号分隔）

### 核心展示字段

- **专注时间段**: 详细的专注时间轴，格式示例：
  ```
  11:44-12:09(25分钟) → [暂停1小时11分钟] → 13:20-17:33(4小时13分钟)
  ```

- **暂停模式**: 暂停情况的统计分析，格式示例：
  ```
  暂停1次(1小时11分钟)
  暂停3次(总计2小时30分钟, 平均50分钟)
  无暂停
  ```

### 分析字段

- **效率(%)**: 实际专注时长占总时长的百分比
- **时间段数量**: 包含的专注时间段数量
- **会话类型**: 会话类型标识
- **实体标签**: 版本控制标签

### 展示示例

| 会话ID | 会话时间 | 总时长 | 暂停时长 | 任务标题 | 专注时间段 | 暂停模式 | 效率(%) |
|--------|----------|--------|----------|----------|------------|----------|---------|
| 6841831a... | 2025-06-05 11:44 - 17:33 | 5小时49分钟 | 1小时11分钟 | 从web端抓包所有api | 11:44-12:09(25分钟) → [暂停1小时11分钟] → 13:20-17:33(4小时13分钟) | 暂停1次(1小时11分钟) | 79.7% |
| 6841599c... | 2025-06-05 08:47 - 10:11 | 1小时24分钟 | 0分钟 | 从web端抓包所有api | 08:47-10:11(1小时24分钟) | 无暂停 | 100.0% |

**重要说明**:
- 每个专注会话只占一行，信息紧凑但完整
- "专注时间段"字段能直观显示专注过程中的暂停和继续情况
- 时间格式自动优化显示（秒/分钟/小时）
- 暂停模式提供统计分析，便于了解专注习惯

## 请求示例

```http
GET http://localhost:8000/custom/export/focus/excel HTTP/1.1
Host: localhost:8000
Accept: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
```

## 响应格式

### 成功响应

**状态码**: `200 OK`

**Content-Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`

**响应头**:
```http
Content-Disposition: attachment; filename=滴答清单专注记录导出_20250106_143022.xlsx
```

**响应体**: Excel文件的二进制数据

### 错误响应

**状态码**: `401 Unauthorized`
```json
{
  "detail": "未设置认证会话，请先完成登录"
}
```

**状态码**: `500 Internal Server Error`
```json
{
  "detail": "导出失败: 具体错误信息"
}
```

## 分页处理说明

专注记录时间线接口支持分页，本导出功能会自动处理分页：

1. **分页参数**: 使用 `to` 参数进行分页，值为上一页最后一条记录的 `startTime`
2. **时间转换**: 自动将时间字符串转换为中国时间戳（+8小时）
3. **自动分页**: 持续获取直到没有更多数据
4. **数据完整性**: 确保获取所有历史专注记录

## 使用说明

1. **认证要求**: 必须先完成认证，设置有效的会话
2. **文件格式**: 导出的Excel文件包含专注记录时间线工作表
3. **文件命名**: 文件名格式为 `滴答清单专注记录导出_YYYYMMDD_HHMMSS.xlsx`
4. **数据完整性**: 包含专注记录的所有字段，无遗漏
5. **分页处理**: 自动处理分页，获取所有历史数据

## 辅助接口

### 获取导出信息

**接口URL**: `http://localhost:8000/custom/export/focus/excel/info`

**请求方法**: `GET`

**功能**: 获取当前用户专注记录的统计信息，用于导出前预览

**响应示例**:
```json
{
  "auth_status": true,
  "focus_records_count_estimate": "31+ (需要分页获取完整数据)",
  "session_info": {
    "has_session": true,
    "session_id": "session_123",
    "created_at": "2025-01-06T14:30:22"
  },
  "note": "专注记录数量为预估值，实际导出时会获取所有历史数据"
}
```

## 注意事项

1. 导出过程可能需要一些时间，特别是当专注记录数量较多时
2. 会通过分页自动获取所有历史专注记录，确保数据完整性
3. 如果数据源获取失败，导出将失败并返回错误信息
4. 建议在网络状况良好时进行导出操作
5. 导出的Excel文件可以用Microsoft Excel、WPS Office等软件打开

## 相关接口

- [获取专注记录时间线](../pomodoros/focus-timeline.md) - 专注记录数据源
- [获取专注详情分布](../pomodoros/focus-distribution.md) - 专注分布统计
- [导出任务到Excel](./export-tasks-excel.md) - 任务导出功能
