# 获取专注时间按小时分布

获取指定日期范围内按小时分布的专注时间统计。

## 接口信息

- **接口URL**: `https://api.dida365.com/api/v2/pomodoros/statistics/dist/clock/{start_date}/{end_date}`
- **请求方法**: `GET`
- **认证要求**: 需要登录认证
- **所属平台**: 滴答清单

## 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| start_date | string | 是 | 开始日期，格式: YYYYMMDD | 20250601 |
| end_date | string | 是 | 结束日期，格式: YYYYMMDD | 20250630 |

## 响应格式

### 成功响应

```json
{
    "0": 120,
    "1": 33,
    "15": 120,
    "16": 72,
    "17": 120,
    "18": 131,
    "19": 135,
    "20": 129,
    "21": 159,
    "22": 120,
    "23": 120
}
```

### 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| {hour} | number | 指定小时的专注时长（分钟），hour为24小时制（0-23） |

### 数据说明

- **时间格式**: 使用24小时制，0表示00:00-01:00，23表示23:00-24:00
- **时长单位**: 分钟
- **统计范围**: 指定日期范围内的总计专注时间
- **数据聚合**: 将整个时间段内相同小时的专注时间累加



## 使用说明

1. 确保已完成认证获取会话
2. 提供有效的日期范围参数
3. 日期格式必须为 YYYYMMDD
4. 返回数据为对象格式，键为小时数（字符串），值为专注时长（数字）
5. 只有有专注记录的小时才会出现在响应中
6. 可用于生成24小时专注时间分布图表

## 应用场景

- **时间管理分析**: 了解自己在一天中哪些时段最专注
- **工作习惯优化**: 根据专注时间分布调整工作安排
- **效率统计**: 分析不同时间段的专注效率
- **数据可视化**: 生成24小时专注热力图或柱状图


